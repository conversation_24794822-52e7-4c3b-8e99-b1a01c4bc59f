# Tailwind CSS Guide

Comprehensive guide to using Tailwind CSS effectively in the enterprise monorepo, including advanced patterns, customization, and best practices.

## 🎯 Tailwind CSS in Our Architecture

### Integration Overview
```mermaid
graph TD
    A[Shared Tailwind Config] --> B[App-Specific Config]
    A --> C[Component Library]
    B --> D[Next.js App]
    C --> E[Shadcn/ui Components]
    D --> F[Custom Components]
    E --> F
    
    style A fill:#06b6d4
    style B fill:#0ea5e9
    style C fill:#3b82f6
```

### Key Benefits
- **Consistent Design System**: Shared tokens across all applications
- **Developer Experience**: IntelliSense and autocomplete
- **Performance**: Optimized CSS output with purging
- **Maintainability**: Centralized theme management

## 🏗️ Configuration Architecture

### Shared Configuration Package
```typescript
// packages/config-tailwind/tailwind.config.ts
import type { Config } from "tailwindcss";
import { fontFamily } from "tailwindcss/defaultTheme";
import animatePlugin from "tailwindcss-animate";

const config = {
  darkMode: ["class"],
  content: [], // Apps define their own content paths
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)", ...fontFamily.sans],
        mono: ["var(--font-mono)", ...fontFamily.mono],
      },
      colors: {
        // Design system colors using CSS variables
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: `var(--radius)`,
        md: `calc(var(--radius) - 2px)`,
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [animatePlugin],
} satisfies Config;

export default config;
```

### Application Configuration
```typescript
// apps/web/tailwind.config.ts
import type { Config } from "tailwindcss";
import sharedConfig from "config-tailwind";

const config: Pick<Config, "content" | "presets"> = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  presets: [sharedConfig],
};

export default config;
```

## 🎨 Design System Implementation

### CSS Variables Setup
```css
/* app/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

### Custom Utility Classes
```css
@layer utilities {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  /* Animation utilities */
  .animate-in {
    animation-duration: 0.2s;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 0.15s;
    animation-fill-mode: both;
  }
}
```

## 🧩 Component Patterns

### Using Class Variance Authority (CVA)
```typescript
// components/ui/button.tsx
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  // Base styles
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

### Responsive Design Patterns
```typescript
// components/responsive-grid.tsx
export function ResponsiveGrid({ children }: { children: React.ReactNode }) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {children}
    </div>
  )
}

// components/responsive-container.tsx
export function ResponsiveContainer({ children }: { children: React.ReactNode }) {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      {children}
    </div>
  )
}
```

## 🌙 Dark Mode Implementation

### Theme Provider
```typescript
// components/theme-provider.tsx
"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

export function ThemeProvider({
  children,
  ...props
}: React.ComponentProps<typeof NextThemesProvider>) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
```

### Theme Toggle Component
```typescript
// components/theme-toggle.tsx
"use client"

import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"

export function ThemeToggle() {
  const { setTheme, theme } = useTheme()

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className="h-9 w-9"
    >
      <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
```

## 🎯 Best Practices

### Class Organization
```typescript
// Good: Organized by category
<div className="
  flex items-center justify-between
  p-4 rounded-lg
  bg-card text-card-foreground
  border border-border
  hover:bg-accent hover:text-accent-foreground
  transition-colors
">
```

### Conditional Classes
```typescript
// Using clsx for conditional classes
import { clsx } from "clsx"

function Alert({ variant, children }: { variant: 'info' | 'warning' | 'error', children: React.ReactNode }) {
  return (
    <div className={clsx(
      'p-4 rounded-md border',
      {
        'bg-blue-50 border-blue-200 text-blue-800': variant === 'info',
        'bg-yellow-50 border-yellow-200 text-yellow-800': variant === 'warning',
        'bg-red-50 border-red-200 text-red-800': variant === 'error',
      }
    )}>
      {children}
    </div>
  )
}
```

### Custom Utilities
```typescript
// lib/utils.ts
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Usage: Merge conflicting classes intelligently
<div className={cn(
  "px-4 py-2", // Base classes
  "px-6", // This will override px-4
  className // User-provided classes
)} />
```

## 🔧 Development Tools

### VS Code Extensions
```json
// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### IntelliSense Configuration
```json
// .vscode/settings.json
{
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

## 📊 Performance Optimization

### CSS Optimization
```javascript
// next.config.mjs
const nextConfig = {
  experimental: {
    optimizeCss: true,
  },
  // Purge unused CSS in production
  webpack: (config, { dev }) => {
    if (!dev) {
      config.optimization.usedExports = true
    }
    return config
  },
}
```

### Bundle Analysis
```bash
# Analyze CSS bundle size
pnpm build && npx bundlesize

# Check for unused CSS
npx purgecss --css dist/**/*.css --content dist/**/*.html
```

## 🧪 Testing Tailwind Classes

### Testing Utilities
```typescript
// __tests__/utils/tailwind.test.ts
import { cn } from '@/lib/utils'

describe('cn utility', () => {
  it('merges classes correctly', () => {
    expect(cn('px-2 py-1', 'px-4')).toBe('py-1 px-4')
  })

  it('handles conditional classes', () => {
    expect(cn('base', true && 'conditional')).toBe('base conditional')
    expect(cn('base', false && 'conditional')).toBe('base')
  })
})
```

### Component Testing
```typescript
// __tests__/components/button.test.tsx
import { render } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button', () => {
  it('applies variant classes correctly', () => {
    const { container } = render(<Button variant="destructive">Delete</Button>)
    const button = container.firstChild as HTMLElement
    expect(button).toHaveClass('bg-destructive')
  })
})
```

## 📋 Tailwind CSS Checklist

### Setup
- [ ] Shared configuration package created
- [ ] App-specific configurations extend shared config
- [ ] CSS variables defined for theming
- [ ] Dark mode configured
- [ ] PostCSS configured

### Development
- [ ] VS Code extensions installed
- [ ] IntelliSense configured for custom patterns
- [ ] Utility classes organized consistently
- [ ] Component variants use CVA
- [ ] Responsive design patterns implemented

### Performance
- [ ] CSS optimization enabled
- [ ] Bundle size monitored
- [ ] Unused CSS purged in production
- [ ] Critical CSS inlined

### Quality
- [ ] Class naming conventions followed
- [ ] Accessibility classes included
- [ ] Testing utilities implemented
- [ ] Documentation updated

---

**🎨 Perfect!** Tailwind CSS is now fully integrated into your enterprise monorepo with shared configurations, design system tokens, and best practices.
