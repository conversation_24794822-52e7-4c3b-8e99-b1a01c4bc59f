# Package Management

Advanced patterns and strategies for managing packages in the enterprise monorepo, including shared libraries, dependency optimization, and publishing workflows.

## 🎯 Package Management Strategy

### Package Types
```mermaid
graph TD
    A[Packages] --> B[Configuration Packages]
    A --> C[Utility Libraries]
    A --> D[UI Components]
    A --> E[Business Logic]
    
    B --> B1[config-typescript]
    B --> B2[config-eslint]
    B --> B3[config-tailwind]
    
    C --> C1[lib-utils]
    C --> C2[lib-validation]
    C --> C3[lib-api-client]
    
    D --> D1[ui-components]
    D --> D2[ui-icons]
    D --> D3[ui-themes]
    
    E --> E1[business-rules]
    E --> E2[domain-models]
    E --> E3[shared-services]
```

### Dependency Management Philosophy
- **Shared Dependencies**: Common tools and utilities
- **Scoped Dependencies**: Package-specific requirements
- **Peer Dependencies**: Let consumers choose versions
- **Dev Dependencies**: Build and development tools

## 📦 Creating Shared Packages

### Utility Library Package
```bash
# Create new utility package
mkdir packages/lib-validation
cd packages/lib-validation
pnpm init
```

```json
// packages/lib-validation/package.json
{
  "name": "lib-validation",
  "version": "0.1.0",
  "private": true,
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "exports": {
    ".": {
      "import": "./dist/index.mjs",
      "require": "./dist/index.js",
      "types": "./dist/index.d.ts"
    },
    "./schemas": {
      "import": "./dist/schemas.mjs",
      "require": "./dist/schemas.js",
      "types": "./dist/schemas.d.ts"
    }
  },
  "files": [
    "dist/**"
  ],
  "scripts": {
    "build": "tsup",
    "dev": "tsup --watch",
    "type-check": "tsc --noEmit",
    "clean": "rm -rf dist"
  },
  "dependencies": {
    "zod": "^3.22.4"
  },
  "devDependencies": {
    "config-typescript": "workspace:*",
    "tsup": "^8.0.1",
    "typescript": "^5.4.5"
  }
}
```

### Build Configuration
```typescript
// packages/lib-validation/tsup.config.ts
import { defineConfig } from 'tsup'

export default defineConfig({
  entry: ['src/index.ts', 'src/schemas.ts'],
  format: ['cjs', 'esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  external: ['react', 'react-dom'],
})
```

### Package Implementation
```typescript
// packages/lib-validation/src/index.ts
export * from './schemas'
export * from './validators'
export * from './types'

// packages/lib-validation/src/schemas.ts
import { z } from 'zod'

export const userSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(2).max(100),
  email: z.string().email(),
  role: z.enum(['admin', 'user', 'viewer']),
  status: z.enum(['active', 'inactive', 'pending']),
})

export const createUserSchema = userSchema.omit({ id: true })
export const updateUserSchema = userSchema.partial().omit({ id: true })

export type User = z.infer<typeof userSchema>
export type CreateUser = z.infer<typeof createUserSchema>
export type UpdateUser = z.infer<typeof updateUserSchema>

// packages/lib-validation/src/validators.ts
import { userSchema, createUserSchema, updateUserSchema } from './schemas'

export function validateUser(data: unknown) {
  return userSchema.safeParse(data)
}

export function validateCreateUser(data: unknown) {
  return createUserSchema.safeParse(data)
}

export function validateUpdateUser(data: unknown) {
  return updateUserSchema.safeParse(data)
}
```

## 🎨 UI Component Package

### Component Library Setup
```json
// packages/ui-components/package.json
{
  "name": "ui-components",
  "version": "0.1.0",
  "private": true,
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "exports": {
    ".": {
      "import": "./dist/index.mjs",
      "require": "./dist/index.js",
      "types": "./dist/index.d.ts"
    },
    "./styles": "./dist/styles.css"
  },
  "files": [
    "dist/**"
  ],
  "scripts": {
    "build": "tsup && tailwindcss -i ./src/styles.css -o ./dist/styles.css",
    "dev": "tsup --watch",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build"
  },
  "dependencies": {
    "@radix-ui/react-slot": "^1.0.2",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.3.0"
  },
  "peerDependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@storybook/react": "^7.6.0",
    "@types/react": "^18.3.3",
    "config-tailwind": "workspace:*",
    "config-typescript": "workspace:*",
    "tailwindcss": "^3.4.4",
    "tsup": "^8.0.1"
  }
}
```

### Component Implementation
```typescript
// packages/ui-components/src/button.tsx
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "./utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

## 🔧 Advanced Dependency Patterns

### Peer Dependencies Strategy
```json
// packages/ui-components/package.json
{
  "peerDependencies": {
    "react": ">=16.8.0",
    "react-dom": ">=16.8.0",
    "tailwindcss": ">=3.0.0"
  },
  "peerDependenciesMeta": {
    "tailwindcss": {
      "optional": true
    }
  }
}
```

### Optional Dependencies
```json
{
  "optionalDependencies": {
    "sharp": "^0.32.0"
  },
  "dependencies": {
    "jimp": "^0.22.0"
  }
}
```

### Dependency Overrides
```json
// Root package.json
{
  "pnpm": {
    "overrides": {
      "react": "^18.2.0",
      "react-dom": "^18.2.0",
      "@types/react": "^18.3.3"
    },
    "patchedDependencies": {
      "some-package@1.0.0": "patches/<EMAIL>"
    }
  }
}
```

## 📋 Package Publishing

### Changeset Configuration
```bash
# Install changesets
pnpm add -DW @changesets/cli

# Initialize changesets
pnpm changeset init
```

```json
// .changeset/config.json
{
  "$schema": "https://unpkg.com/@changesets/config@2.3.1/schema.json",
  "changelog": "@changesets/cli/changelog",
  "commit": false,
  "fixed": [],
  "linked": [],
  "access": "restricted",
  "baseBranch": "main",
  "updateInternalDependencies": "patch",
  "ignore": ["docs"]
}
```

### Release Workflow
```yaml
# .github/workflows/release.yml
name: Release

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: pnpm build

      - name: Create Release Pull Request or Publish
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm changeset publish
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
```

## 🔄 Workspace Commands

### Package-Specific Commands
```bash
# Install dependency to specific package
pnpm --filter ui-components add react-hook-form

# Run script in specific package
pnpm --filter lib-validation build

# Run script in multiple packages
pnpm --filter "{ui-components,lib-validation}" build

# Run script in packages that depend on a package
pnpm --filter ...ui-components test
```

### Workspace Utilities
```json
// Root package.json scripts
{
  "scripts": {
    "build:packages": "pnpm --filter './packages/*' build",
    "test:packages": "pnpm --filter './packages/*' test",
    "clean:packages": "pnpm --filter './packages/*' clean",
    "version:packages": "changeset version",
    "publish:packages": "changeset publish"
  }
}
```

## 🧪 Package Testing

### Package-Level Testing
```typescript
// packages/lib-validation/__tests__/schemas.test.ts
import { describe, it, expect } from 'vitest'
import { validateUser, validateCreateUser } from '../src'

describe('User Validation', () => {
  it('validates correct user data', () => {
    const validUser = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
    }

    const result = validateUser(validUser)
    expect(result.success).toBe(true)
  })

  it('rejects invalid user data', () => {
    const invalidUser = {
      name: 'J', // Too short
      email: 'invalid-email',
      role: 'invalid-role',
    }

    const result = validateCreateUser(invalidUser)
    expect(result.success).toBe(false)
    expect(result.error?.issues).toHaveLength(3)
  })
})
```

### Integration Testing
```typescript
// apps/web/__tests__/package-integration.test.ts
import { validateUser } from 'lib-validation'
import { Button } from 'ui-components'
import { render, screen } from '@testing-library/react'

describe('Package Integration', () => {
  it('uses validation package correctly', () => {
    const user = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user' as const,
      status: 'active' as const,
    }

    const result = validateUser(user)
    expect(result.success).toBe(true)
  })

  it('renders UI components correctly', () => {
    render(<Button>Test Button</Button>)
    expect(screen.getByRole('button')).toHaveTextContent('Test Button')
  })
})
```

## 📊 Package Analytics

### Bundle Size Monitoring
```json
// packages/ui-components/package.json
{
  "scripts": {
    "size": "size-limit",
    "size:why": "size-limit --why"
  },
  "size-limit": [
    {
      "path": "dist/index.js",
      "limit": "50 KB"
    },
    {
      "path": "dist/button.js",
      "limit": "5 KB"
    }
  ]
}
```

### Dependency Analysis
```bash
# Analyze package dependencies
pnpm --filter ui-components exec -- npx depcheck

# Check for circular dependencies
pnpm --filter ui-components exec -- npx madge --circular src/

# Visualize dependency graph
pnpm --filter ui-components exec -- npx madge --image deps.png src/
```

## 📋 Package Management Checklist

### Package Creation
- [ ] Package.json configured correctly
- [ ] Build system setup (tsup/rollup)
- [ ] TypeScript configuration
- [ ] Export maps defined
- [ ] Peer dependencies specified

### Quality Assurance
- [ ] Tests written and passing
- [ ] Type definitions generated
- [ ] Bundle size within limits
- [ ] No circular dependencies
- [ ] Documentation complete

### Publishing
- [ ] Changeset created
- [ ] Version bumped appropriately
- [ ] Build artifacts generated
- [ ] Tests passing in CI
- [ ] Published to registry

### Maintenance
- [ ] Dependencies updated regularly
- [ ] Security vulnerabilities addressed
- [ ] Breaking changes documented
- [ ] Migration guides provided
- [ ] Deprecation notices added

---

**Next:** [Code Generation](./17-code-generation.md) for automation and scaffolding tools.
