# Code Generation

Automation tools and scaffolding systems to accelerate development and maintain consistency across the enterprise monorepo.

## 🎯 Code Generation Strategy

### Generation Types
```mermaid
graph TD
    A[Code Generation] --> B[Component Generation]
    A --> C[API Generation]
    A --> D[Type Generation]
    A --> E[Configuration Generation]
    
    B --> B1[React Components]
    B --> B2[Storybook Stories]
    B --> B3[Test Files]
    
    C --> C1[API Routes]
    C --> C2[Service Classes]
    C --> C3[Hook Generators]
    
    D --> D1[TypeScript Types]
    D --> D2[Zod Schemas]
    D --> D3[Database Types]
    
    E --> E1[Package Configs]
    E --> E2[Build Scripts]
    E --> E3[CI/CD Workflows]
```

### Benefits
- **Consistency**: Standardized code patterns
- **Speed**: Rapid scaffolding of common structures
- **Quality**: Built-in best practices
- **Maintenance**: Easy updates across codebase

## 🛠️ Plop.js Setup

### Installation and Configuration
```bash
# Install Plop.js
pnpm add -DW plop

# Create generators directory
mkdir tools/generators
```

```javascript
// plopfile.js
module.exports = function (plop) {
  // Component generator
  plop.setGenerator('component', {
    description: 'Create a new React component',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: 'Component name:',
        validate: (value) => {
          if (!value) return 'Component name is required'
          if (!/^[A-Z][a-zA-Z0-9]*$/.test(value)) {
            return 'Component name must be PascalCase'
          }
          return true
        },
      },
      {
        type: 'list',
        name: 'type',
        message: 'Component type:',
        choices: ['ui', 'feature', 'layout', 'page'],
        default: 'ui',
      },
      {
        type: 'confirm',
        name: 'withStory',
        message: 'Include Storybook story?',
        default: true,
      },
      {
        type: 'confirm',
        name: 'withTest',
        message: 'Include test file?',
        default: true,
      },
    ],
    actions: (data) => {
      const actions = []
      
      // Component file
      actions.push({
        type: 'add',
        path: 'apps/web/components/{{type}}/{{kebabCase name}}/{{kebabCase name}}.tsx',
        templateFile: 'tools/generators/component/component.hbs',
      })
      
      // Index file
      actions.push({
        type: 'add',
        path: 'apps/web/components/{{type}}/{{kebabCase name}}/index.ts',
        templateFile: 'tools/generators/component/index.hbs',
      })
      
      // Storybook story
      if (data.withStory) {
        actions.push({
          type: 'add',
          path: 'apps/web/components/{{type}}/{{kebabCase name}}/{{kebabCase name}}.stories.tsx',
          templateFile: 'tools/generators/component/story.hbs',
        })
      }
      
      // Test file
      if (data.withTest) {
        actions.push({
          type: 'add',
          path: 'apps/web/components/{{type}}/{{kebabCase name}}/__tests__/{{kebabCase name}}.test.tsx',
          templateFile: 'tools/generators/component/test.hbs',
        })
      }
      
      return actions
    },
  })

  // Feature generator
  plop.setGenerator('feature', {
    description: 'Create a new feature with CRUD operations',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: 'Feature name (singular):',
        validate: (value) => {
          if (!value) return 'Feature name is required'
          return true
        },
      },
      {
        type: 'input',
        name: 'pluralName',
        message: 'Plural name:',
        default: (answers) => `${answers.name}s`,
      },
      {
        type: 'checkbox',
        name: 'operations',
        message: 'Select CRUD operations:',
        choices: [
          { name: 'Create', value: 'create', checked: true },
          { name: 'Read', value: 'read', checked: true },
          { name: 'Update', value: 'update', checked: true },
          { name: 'Delete', value: 'delete', checked: true },
        ],
      },
    ],
    actions: [
      // Types
      {
        type: 'add',
        path: 'apps/web/types/{{kebabCase name}}.ts',
        templateFile: 'tools/generators/feature/types.hbs',
      },
      // Service
      {
        type: 'add',
        path: 'apps/web/services/{{kebabCase name}}-service.ts',
        templateFile: 'tools/generators/feature/service.hbs',
      },
      // Hooks
      {
        type: 'add',
        path: 'apps/web/hooks/queries/use-{{kebabCase pluralName}}-query.ts',
        templateFile: 'tools/generators/feature/hooks.hbs',
      },
      // Components
      {
        type: 'add',
        path: 'apps/web/components/features/{{kebabCase pluralName}}/{{kebabCase name}}-list.tsx',
        templateFile: 'tools/generators/feature/list-component.hbs',
      },
      {
        type: 'add',
        path: 'apps/web/components/features/{{kebabCase pluralName}}/{{kebabCase name}}-form.tsx',
        templateFile: 'tools/generators/feature/form-component.hbs',
      },
      // Page
      {
        type: 'add',
        path: 'apps/web/app/(dashboard)/{{kebabCase pluralName}}/page.tsx',
        templateFile: 'tools/generators/feature/page.hbs',
      },
    ],
  })

  // Package generator
  plop.setGenerator('package', {
    description: 'Create a new shared package',
    prompts: [
      {
        type: 'input',
        name: 'name',
        message: 'Package name:',
        validate: (value) => {
          if (!value) return 'Package name is required'
          if (!/^[a-z][a-z0-9-]*$/.test(value)) {
            return 'Package name must be kebab-case'
          }
          return true
        },
      },
      {
        type: 'list',
        name: 'type',
        message: 'Package type:',
        choices: ['lib', 'config', 'ui'],
        default: 'lib',
      },
      {
        type: 'input',
        name: 'description',
        message: 'Package description:',
      },
    ],
    actions: [
      {
        type: 'add',
        path: 'packages/{{type}}-{{name}}/package.json',
        templateFile: 'tools/generators/package/package.json.hbs',
      },
      {
        type: 'add',
        path: 'packages/{{type}}-{{name}}/src/index.ts',
        templateFile: 'tools/generators/package/index.hbs',
      },
      {
        type: 'add',
        path: 'packages/{{type}}-{{name}}/tsconfig.json',
        templateFile: 'tools/generators/package/tsconfig.hbs',
      },
      {
        type: 'add',
        path: 'packages/{{type}}-{{name}}/tsup.config.ts',
        templateFile: 'tools/generators/package/tsup.config.hbs',
      },
    ],
  })
}
```

## 📝 Generator Templates

### Component Template
```handlebars
{{!-- tools/generators/component/component.hbs --}}
import { cn } from '@/lib/utils'

interface {{pascalCase name}}Props {
  className?: string
  children?: React.ReactNode
}

export function {{pascalCase name}}({ className, children }: {{pascalCase name}}Props) {
  return (
    <div className={cn('{{kebabCase name}}', className)}>
      {children}
    </div>
  )
}
```

### Test Template
```handlebars
{{!-- tools/generators/component/test.hbs --}}
import { render, screen } from '@testing-library/react'
import { {{pascalCase name}} } from '../{{kebabCase name}}'

describe('{{pascalCase name}}', () => {
  it('renders correctly', () => {
    render(<{{pascalCase name}}>Test content</{{pascalCase name}}>)
    expect(screen.getByText('Test content')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<{{pascalCase name}} className="custom-class">Test</{{pascalCase name}}>)
    const element = screen.getByText('Test')
    expect(element).toHaveClass('custom-class')
  })
})
```

### Feature Service Template
```handlebars
{{!-- tools/generators/feature/service.hbs --}}
import { api } from './api'
import { {{pascalCase name}}, Create{{pascalCase name}}Data, Update{{pascalCase name}}Data } from '@/types/{{kebabCase name}}'

export interface Get{{pascalCase pluralName}}Params {
  page?: number
  limit?: number
  search?: string
}

export const {{pascalCase name}}Service = {
{{#if (includes operations 'read')}}
  get{{pascalCase pluralName}}: (params: Get{{pascalCase pluralName}}Params = {}) => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString())
      }
    })
    return api.get<{{pascalCase name}}[]>(`/{{kebabCase pluralName}}?${searchParams}`)
  },

  get{{pascalCase name}}: (id: string) => api.get<{{pascalCase name}}>(`/{{kebabCase pluralName}}/${id}`),
{{/if}}

{{#if (includes operations 'create')}}
  create{{pascalCase name}}: (data: Create{{pascalCase name}}Data) => api.post<{{pascalCase name}}>('/{{kebabCase pluralName}}', data),
{{/if}}

{{#if (includes operations 'update')}}
  update{{pascalCase name}}: (id: string, data: Update{{pascalCase name}}Data) =>
    api.patch<{{pascalCase name}}>(`/{{kebabCase pluralName}}/${id}`, data),
{{/if}}

{{#if (includes operations 'delete')}}
  delete{{pascalCase name}}: (id: string) => api.delete<void>(`/{{kebabCase pluralName}}/${id}`),
{{/if}}
}
```

## 🤖 OpenAPI Code Generation

### OpenAPI Generator Setup
```bash
# Install OpenAPI generator
pnpm add -DW @openapitools/openapi-generator-cli

# Configure generator
npx openapi-generator-cli version-manager set 6.6.0
```

```json
// openapitools.json
{
  "generator-cli": {
    "version": "6.6.0",
    "generators": {
      "typescript-fetch": {
        "generatorName": "typescript-fetch",
        "output": "./apps/web/generated/api",
        "glob": "./api-spec.yaml",
        "additionalProperties": {
          "typescriptThreePlus": true,
          "supportsES6": true,
          "npmName": "api-client",
          "npmVersion": "1.0.0"
        }
      }
    }
  }
}
```

### API Generation Script
```bash
#!/bin/bash
# tools/generate-api.sh

echo "Generating API client from OpenAPI spec..."

# Generate TypeScript client
npx openapi-generator-cli generate \
  -i ./api-spec.yaml \
  -g typescript-fetch \
  -o ./apps/web/generated/api \
  --additional-properties=typescriptThreePlus=true,supportsES6=true

# Generate Zod schemas
npx openapi-zod-client ./api-spec.yaml -o ./apps/web/generated/schemas.ts

echo "API client generated successfully!"
```

## 🔄 Database Code Generation

### Prisma Schema Generation
```bash
# Install Prisma
pnpm add prisma @prisma/client

# Initialize Prisma
npx prisma init
```

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
  output   = "../apps/web/generated/prisma"
}

generator zod {
  provider = "zod-prisma-types"
  output   = "../apps/web/generated/zod"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum Role {
  ADMIN
  USER
  VIEWER
}
```

### Type Generation Script
```typescript
// tools/generate-types.ts
import { PrismaClient } from '@prisma/client'
import fs from 'fs/promises'
import path from 'path'

const prisma = new PrismaClient()

async function generateTypes() {
  try {
    // Generate database types
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `

    let typeDefinitions = '// Auto-generated database types\n\n'

    for (const table of tables as any[]) {
      const columns = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = ${table.table_name}
      `

      typeDefinitions += generateTableType(table.table_name, columns as any[])
    }

    await fs.writeFile(
      path.join(process.cwd(), 'apps/web/generated/database-types.ts'),
      typeDefinitions
    )

    console.log('Database types generated successfully!')
  } catch (error) {
    console.error('Error generating types:', error)
  } finally {
    await prisma.$disconnect()
  }
}

function generateTableType(tableName: string, columns: any[]): string {
  const typeName = toPascalCase(tableName)
  let typeDefinition = `export interface ${typeName} {\n`

  for (const column of columns) {
    const tsType = mapPostgresToTypeScript(column.data_type)
    const optional = column.is_nullable === 'YES' ? '?' : ''
    typeDefinition += `  ${column.column_name}${optional}: ${tsType}\n`
  }

  typeDefinition += '}\n\n'
  return typeDefinition
}

function toPascalCase(str: string): string {
  return str.replace(/(^\w|_\w)/g, (match) => 
    match.replace('_', '').toUpperCase()
  )
}

function mapPostgresToTypeScript(pgType: string): string {
  const typeMap: Record<string, string> = {
    'character varying': 'string',
    'text': 'string',
    'integer': 'number',
    'bigint': 'number',
    'boolean': 'boolean',
    'timestamp with time zone': 'Date',
    'timestamp without time zone': 'Date',
    'uuid': 'string',
  }

  return typeMap[pgType] || 'unknown'
}

generateTypes()
```

## 📋 Custom CLI Tool

### CLI Setup
```bash
# Create CLI package
mkdir tools/cli
cd tools/cli
pnpm init
```

```json
// tools/cli/package.json
{
  "name": "enterprise-cli",
  "version": "1.0.0",
  "bin": {
    "enterprise": "./dist/index.js"
  },
  "scripts": {
    "build": "tsup src/index.ts --format cjs --target node16",
    "dev": "tsup src/index.ts --format cjs --target node16 --watch"
  },
  "dependencies": {
    "commander": "^11.1.0",
    "inquirer": "^9.2.12",
    "chalk": "^5.3.0",
    "ora": "^7.0.1"
  }
}
```

### CLI Implementation
```typescript
// tools/cli/src/index.ts
#!/usr/bin/env node

import { Command } from 'commander'
import chalk from 'chalk'
import { generateComponent } from './commands/generate-component'
import { generateFeature } from './commands/generate-feature'
import { setupProject } from './commands/setup-project'

const program = new Command()

program
  .name('enterprise')
  .description('Enterprise monorepo CLI tool')
  .version('1.0.0')

program
  .command('generate:component')
  .alias('g:c')
  .description('Generate a new React component')
  .action(generateComponent)

program
  .command('generate:feature')
  .alias('g:f')
  .description('Generate a new feature with CRUD operations')
  .action(generateFeature)

program
  .command('setup')
  .description('Setup new project from template')
  .action(setupProject)

program.parse()
```

## 📊 Generation Scripts

### Package Scripts
```json
// Root package.json
{
  "scripts": {
    "generate": "plop",
    "generate:component": "plop component",
    "generate:feature": "plop feature",
    "generate:package": "plop package",
    "generate:api": "./tools/generate-api.sh",
    "generate:types": "tsx tools/generate-types.ts",
    "generate:prisma": "prisma generate",
    "postinstall": "pnpm generate:prisma"
  }
}
```

### Automation Workflow
```yaml
# .github/workflows/generate.yml
name: Code Generation

on:
  schedule:
    - cron: '0 2 * * *' # Daily at 2 AM
  workflow_dispatch:

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Generate API client
        run: pnpm generate:api
        
      - name: Generate database types
        run: pnpm generate:types
        
      - name: Create PR if changes
        uses: peter-evans/create-pull-request@v5
        with:
          title: 'chore: update generated code'
          body: 'Auto-generated code updates'
          branch: 'chore/update-generated-code'
```

## 📋 Code Generation Checklist

### Setup
- [ ] Plop.js configured
- [ ] Generator templates created
- [ ] CLI tool implemented
- [ ] Scripts added to package.json

### Templates
- [ ] Component templates
- [ ] Feature templates
- [ ] Package templates
- [ ] Test templates
- [ ] Story templates

### Automation
- [ ] API generation setup
- [ ] Type generation configured
- [ ] Database schema sync
- [ ] CI/CD integration

### Quality
- [ ] Generated code follows standards
- [ ] Templates include best practices
- [ ] Error handling implemented
- [ ] Documentation generated

---

**🎉 Complete!** You now have all the documentation files for implementing the enterprise-grade monorepo architecture.
