# Performance Optimization

Comprehensive guide to optimizing performance in the enterprise monorepo, covering build performance, runtime optimization, and monitoring.

## 🎯 Performance Strategy

### Performance Metrics
```mermaid
graph TD
    A[Performance Metrics] --> B[Build Performance]
    A --> C[Runtime Performance]
    A --> D[User Experience]

    B --> B1[Build Time]
    B --> B2[Bundle Size]
    B --> B3[Cache Hit Rate]

    C --> C1[First Contentful Paint]
    C --> C2[Largest Contentful Paint]
    C --> C3[Time to Interactive]

    D --> D1[Core Web Vitals]
    D --> D2[Lighthouse Score]
    D --> D3[Real User Metrics]
```

### Performance Targets
- **Build Time**: < 30s for incremental builds
- **Bundle Size**: < 200KB initial JavaScript
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **Lighthouse Score**: > 90

## 🏗️ Build Performance

### Turborepo Optimization
```json
// turbo.json
{
  "$schema": "https://turbo.build/schema.json",
  "globalDependencies": [
    "**/.env.*local",
    "tsconfig.json"
  ],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "inputs": [
        "$TURBO_DEFAULT$",
        "!**/*.test.*",
        "!**/*.spec.*",
        "!**/*.stories.*"
      ],
      "outputs": [
        ".next/**",
        "!.next/cache/**",
        "dist/**"
      ],
      "env": ["NODE_ENV", "NEXT_PUBLIC_*"]
    }
  },
  "remoteCache": {
    "signature": true
  }
}
```

### Next.js Build Optimization
```javascript
// apps/web/next.config.mjs
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Experimental optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'date-fns',
      'lodash-es'
    ],
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // Bundle optimization
  webpack: (config, { dev, isServer }) => {
    // Optimize bundle splitting
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: 10,
            reuseExistingChunk: true,
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      }
    }

    return config
  },

  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
}

export default nextConfig
```

### TypeScript Performance
```json
// tsconfig.json
{
  "compilerOptions": {
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo",
    "skipLibCheck": true,
    "skipDefaultLibCheck": true
  },
  "exclude": [
    "node_modules",
    "**/*.test.*",
    "**/*.spec.*",
    "coverage"
  ]
}
```

## ⚡ Runtime Performance

### Code Splitting Strategies
```typescript
// Dynamic imports for large components
import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Lazy load heavy components
const DataVisualization = dynamic(
  () => import('@/components/data-visualization'),
  {
    loading: () => <div>Loading chart...</div>,
    ssr: false, // Disable SSR for client-only components
  }
)

// Route-based code splitting
const AdminPanel = dynamic(
  () => import('@/components/admin/admin-panel'),
  {
    loading: () => <AdminPanelSkeleton />,
  }
)

// Conditional loading
const AdvancedFeatures = dynamic(
  () => import('@/components/advanced-features'),
  {
    loading: () => <FeaturesSkeleton />,
  }
)

export function Dashboard() {
  const { user } = useAuth()

  return (
    <div>
      <h1>Dashboard</h1>

      <Suspense fallback={<ChartSkeleton />}>
        <DataVisualization />
      </Suspense>

      {user?.role === 'admin' && (
        <Suspense fallback={<AdminSkeleton />}>
          <AdminPanel />
        </Suspense>
      )}
    </div>
  )
}
```

### Image Optimization
```typescript
// apps/web/components/optimized-image.tsx
import Image from 'next/image'
import { useState } from 'react'

interface OptimizedImageProps {
  src: string
  alt: string
  width: number
  height: number
  priority?: boolean
  className?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  className,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        quality={85}
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
        onLoad={() => setIsLoading(false)}
        className={`
          duration-700 ease-in-out
          ${isLoading ? 'scale-110 blur-2xl grayscale' : 'scale-100 blur-0 grayscale-0'}
        `}
      />
    </div>
  )
}
```

### Font Optimization
```typescript
// apps/web/app/layout.tsx
import { Inter, Roboto_Mono } from 'next/font/google'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

const robotoMono = Roboto_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto-mono',
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${robotoMono.variable}`}>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  )
}
```

## 🔄 State Management Performance

### TanStack Query Optimization
```typescript
// apps/web/lib/query-client.ts
import { QueryClient } from '@tanstack/react-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Reduce network requests
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes

      // Optimize refetching
      refetchOnWindowFocus: false,
      refetchOnReconnect: 'always',

      // Retry configuration
      retry: (failureCount, error: any) => {
        if (error?.status === 404) return false
        return failureCount < 3
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
})

// Prefetch critical data
export function prefetchCriticalData() {
  queryClient.prefetchQuery({
    queryKey: ['user'],
    queryFn: () => UserService.getCurrentUser(),
    staleTime: 1000 * 60 * 10, // 10 minutes
  })
}
```

### Optimistic Updates
```typescript
// apps/web/hooks/mutations/use-optimistic-user-update.ts
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { UserService } from '@/services/user-service'

export function useOptimisticUserUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserData }) =>
      UserService.updateUser(id, data),

    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['users', id] })

      // Snapshot previous value
      const previousUser = queryClient.getQueryData(['users', id])

      // Optimistically update
      queryClient.setQueryData(['users', id], (old: any) => ({
        ...old,
        ...data,
      }))

      return { previousUser }
    },

    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousUser) {
        queryClient.setQueryData(['users', variables.id], context.previousUser)
      }
    },

    onSettled: (data, error, variables) => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['users', variables.id] })
    },
  })
}
```

## 🎨 UI Performance

### Virtual Scrolling for Large Lists
```typescript
// apps/web/components/virtual-list.tsx
import { FixedSizeList as List } from 'react-window'
import { memo } from 'react'

interface VirtualListProps {
  items: any[]
  height: number
  itemHeight: number
  renderItem: (props: { index: number; style: any; data: any }) => JSX.Element
}

const VirtualList = memo(function VirtualList({
  items,
  height,
  itemHeight,
  renderItem,
}: VirtualListProps) {
  return (
    <List
      height={height}
      itemCount={items.length}
      itemSize={itemHeight}
      itemData={items}
      overscanCount={5} // Render 5 extra items for smooth scrolling
    >
      {renderItem}
    </List>
  )
})

// Usage
export function UserList({ users }: { users: User[] }) {
  const renderUser = memo(({ index, style, data }: any) => (
    <div style={style}>
      <UserCard user={data[index]} />
    </div>
  ))

  return (
    <VirtualList
      items={users}
      height={600}
      itemHeight={80}
      renderItem={renderUser}
    />
  )
}
```

### Memoization Strategies
```typescript
// apps/web/components/expensive-component.tsx
import { memo, useMemo, useCallback } from 'react'

interface ExpensiveComponentProps {
  data: ComplexData[]
  onItemClick: (id: string) => void
  filters: FilterOptions
}

export const ExpensiveComponent = memo(function ExpensiveComponent({
  data,
  onItemClick,
  filters,
}: ExpensiveComponentProps) {
  // Memoize expensive calculations
  const processedData = useMemo(() => {
    return data
      .filter(item => applyFilters(item, filters))
      .sort((a, b) => a.priority - b.priority)
      .map(item => enhanceItem(item))
  }, [data, filters])

  // Memoize event handlers
  const handleItemClick = useCallback((id: string) => {
    onItemClick(id)
  }, [onItemClick])

  return (
    <div>
      {processedData.map(item => (
        <ExpensiveItem
          key={item.id}
          item={item}
          onClick={handleItemClick}
        />
      ))}
    </div>
  )
})

// Memoize child components
const ExpensiveItem = memo(function ExpensiveItem({
  item,
  onClick,
}: {
  item: ProcessedItem
  onClick: (id: string) => void
}) {
  return (
    <div onClick={() => onClick(item.id)}>
      {/* Complex rendering logic */}
    </div>
  )
})
```

## 📊 Performance Monitoring

### Web Vitals Tracking
```typescript
// apps/web/lib/web-vitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric: any) {
  // Send to your analytics service
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    })
  }
}

export function trackWebVitals() {
  getCLS(sendToAnalytics)
  getFID(sendToAnalytics)
  getFCP(sendToAnalytics)
  getLCP(sendToAnalytics)
  getTTFB(sendToAnalytics)
}
```

### Performance Monitoring Component
```typescript
// apps/web/components/performance-monitor.tsx
'use client'

import { useEffect } from 'react'
import { trackWebVitals } from '@/lib/web-vitals'

export function PerformanceMonitor() {
  useEffect(() => {
    // Track web vitals
    trackWebVitals()

    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn('Long task detected:', entry)
          }
        }
      })
      observer.observe({ entryTypes: ['longtask'] })

      return () => observer.disconnect()
    }
  }, [])

  return null
}
```

## 🔧 Bundle Analysis

### Bundle Analyzer Setup
```json
{
  "scripts": {
    "analyze": "ANALYZE=true pnpm build",
    "analyze:server": "BUNDLE_ANALYZE=server pnpm build",
    "analyze:browser": "BUNDLE_ANALYZE=browser pnpm build"
  }
}
```

```javascript
// apps/web/next.config.mjs
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer(nextConfig)
```

### Performance Budget
```json
// apps/web/performance-budget.json
{
  "budget": [
    {
      "path": "/_next/static/chunks/*.js",
      "maximumFileSizeByte": 244000
    },
    {
      "path": "/_next/static/css/*.css",
      "maximumFileSizeByte": 16000
    }
  ]
}
```

## 📋 Performance Checklist

### Build Performance
- [ ] Turborepo caching enabled
- [ ] TypeScript incremental compilation
- [ ] Bundle analysis completed
- [ ] Tree shaking verified
- [ ] Dead code elimination

### Runtime Performance
- [ ] Code splitting implemented
- [ ] Images optimized
- [ ] Fonts optimized
- [ ] Critical CSS inlined
- [ ] Service worker configured

### Monitoring
- [ ] Web Vitals tracking
- [ ] Performance budgets set
- [ ] Real User Monitoring (RUM)
- [ ] Lighthouse CI integrated
- [ ] Performance alerts configured

---

**Next:** [Package Management](./16-package-management.md) for advanced monorepo patterns.