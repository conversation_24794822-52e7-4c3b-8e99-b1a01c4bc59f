# Troubleshooting

Common issues and solutions for the enterprise monorepo setup and development workflow.

## 🚨 Installation Issues

### Node.js Version Conflicts
**Problem**: Different Node.js versions causing compatibility issues.

**Solution**:
```bash
# Use Node Version Manager
nvm install 18.17.0
nvm use 18.17.0
nvm alias default 18.17.0

# Verify version
node --version
```

### pnpm Installation Issues
**Problem**: pnpm not found or permission errors.

**Solutions**:
```bash
# Install pnpm globally
npm install -g pnpm

# Or use corepack (Node.js 16.13+)
corepack enable
corepack prepare pnpm@latest --activate

# Fix permissions (macOS/Linux)
sudo chown -R $(whoami) ~/.pnpm
```

### Package Installation Failures
**Problem**: Dependencies fail to install.

**Solutions**:
```bash
# Clear caches
pnpm store prune
rm -rf node_modules
rm pnpm-lock.yaml

# Reinstall
pnpm install

# If still failing, try with legacy peer deps
pnpm install --legacy-peer-deps
```

## 🏗️ Build Issues

### Turborepo Cache Issues
**Problem**: Stale cache causing build failures.

**Solutions**:
```bash
# Clear Turborepo cache
turbo prune

# Force rebuild without cache
turbo build --force

# Clear specific package cache
turbo prune --scope=web
```

### Next.js Build Errors
**Problem**: Next.js compilation failures.

**Solutions**:
```bash
# Clear Next.js cache
rm -rf apps/web/.next

# Check for TypeScript errors
pnpm --filter web type-check

# Build with verbose output
pnpm --filter web build -- --debug
```

### TypeScript Compilation Errors
**Problem**: TypeScript errors preventing builds.

**Solutions**:
```bash
# Check TypeScript configuration
pnpm --filter web exec -- tsc --showConfig

# Verify TypeScript version
pnpm --filter web exec -- tsc --version

# Clear TypeScript cache
rm -rf apps/web/.tsbuildinfo
```

## 🔧 Development Server Issues

### Port Already in Use
**Problem**: Development server can't start due to port conflicts.

**Solutions**:
```bash
# Find process using port 3000
lsof -ti:3000

# Kill process
lsof -ti:3000 | xargs kill -9

# Or use different port
pnpm --filter web dev -- --port 3001
```

### Hot Reload Not Working
**Problem**: Changes not reflecting in browser.

**Solutions**:
```bash
# Restart development server
pnpm dev

# Clear browser cache
# Chrome: Cmd/Ctrl + Shift + R

# Check file watchers (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### Environment Variables Not Loading
**Problem**: Environment variables not accessible.

**Solutions**:
```bash
# Check .env file location (should be in app root)
ls -la apps/web/.env*

# Verify variable names start with NEXT_PUBLIC_ for client-side
# Example: NEXT_PUBLIC_API_URL=http://localhost:3001

# Restart development server after env changes
pnpm dev
```

## 📦 Package Management Issues

### Workspace Dependencies Not Resolving
**Problem**: Shared packages not found.

**Solutions**:
```bash
# Verify workspace configuration
cat pnpm-workspace.yaml

# Check package.json dependencies
cat apps/web/package.json | grep "workspace:"

# Reinstall workspace dependencies
pnpm install
```

### Version Conflicts
**Problem**: Conflicting dependency versions.

**Solutions**:
```bash
# Check for conflicts
pnpm list --depth=2

# Update to compatible versions
pnpm update

# Use resolutions in package.json
{
  "pnpm": {
    "overrides": {
      "react": "^18.2.0"
    }
  }
}
```

### Peer Dependency Warnings
**Problem**: Peer dependency warnings during installation.

**Solutions**:
```bash
# Install missing peer dependencies
pnpm add react@^18.2.0 react-dom@^18.2.0

# Or suppress warnings (not recommended)
pnpm install --no-optional
```

## 🎨 UI and Styling Issues

### Tailwind CSS Not Working
**Problem**: Tailwind classes not applying.

**Solutions**:
```bash
# Verify Tailwind configuration
cat apps/web/tailwind.config.ts

# Check content paths include your files
content: [
  './app/**/*.{js,ts,jsx,tsx,mdx}',
  './components/**/*.{js,ts,jsx,tsx,mdx}',
]

# Restart development server
pnpm dev
```

### Shadcn/ui Components Not Found
**Problem**: Shadcn/ui components throwing import errors.

**Solutions**:
```bash
# Verify component installation
ls apps/web/components/ui/

# Reinstall component
pnpm --filter web dlx shadcn-ui@latest add button

# Check import paths
import { Button } from "@/components/ui/button"
```

### CSS Variables Not Working
**Problem**: CSS custom properties not applying.

**Solutions**:
```css
/* Verify CSS variables in globals.css */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    /* ... other variables */
  }
}

/* Check if globals.css is imported in layout.tsx */
import './globals.css'
```

## 🔄 State Management Issues

### TanStack Query Not Caching
**Problem**: Queries refetching unnecessarily.

**Solutions**:
```typescript
// Check query keys are consistent
const { data } = useQuery({
  queryKey: ['users', params], // Ensure params are serializable
  queryFn: () => UserService.getUsers(params),
  staleTime: 1000 * 60 * 5, // Add stale time
})

// Verify QueryClient configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,
      gcTime: 1000 * 60 * 30,
    },
  },
})
```

### Zustand Store Not Persisting
**Problem**: Store state not persisting across sessions.

**Solutions**:
```typescript
// Verify persist middleware configuration
export const useStore = create<State & Actions>()(
  devtools(
    persist(
      (set, get) => ({
        // state and actions
      }),
      {
        name: 'app-store', // Unique name
        partialize: (state) => ({ 
          // Only persist specific fields
          theme: state.theme 
        }),
      }
    )
  )
)
```

### Hydration Mismatches
**Problem**: Server/client state mismatches.

**Solutions**:
```typescript
// Use dynamic imports for client-only components
import dynamic from 'next/dynamic'

const ClientOnlyComponent = dynamic(
  () => import('./client-component'),
  { ssr: false }
)

// Or check if running on client
const [mounted, setMounted] = useState(false)

useEffect(() => {
  setMounted(true)
}, [])

if (!mounted) return null
```

## 🧪 Testing Issues

### Tests Not Finding Components
**Problem**: Test files can't import components.

**Solutions**:
```typescript
// Verify Jest configuration supports path mapping
// jest.config.js
module.exports = {
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
}

// Or use relative imports in tests
import { UserCard } from '../user-card'
```

### Mock Issues
**Problem**: Mocks not working correctly.

**Solutions**:
```typescript
// Mock API calls
jest.mock('@/services/user-service', () => ({
  UserService: {
    getUsers: jest.fn().mockResolvedValue([]),
  },
}))

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
}))
```

## 🚀 Deployment Issues

### Build Failures in Production
**Problem**: Production builds failing.

**Solutions**:
```bash
# Test production build locally
NODE_ENV=production pnpm build

# Check for environment-specific issues
NODE_ENV=production pnpm --filter web build

# Verify environment variables
echo $NODE_ENV
```

### Memory Issues During Build
**Problem**: Out of memory errors during build.

**Solutions**:
```bash
# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=4096" pnpm build

# Or in package.json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' turbo build"
  }
}
```

## 🔍 Debugging Tools

### Enable Debug Logging
```bash
# Turborepo debug
DEBUG=turbo:* pnpm build

# Next.js debug
DEBUG=* pnpm --filter web dev

# pnpm debug
pnpm --loglevel debug install
```

### Performance Profiling
```bash
# Profile Turborepo builds
turbo build --profile=profile.json

# Analyze profile
turbo analyze profile.json

# Next.js bundle analysis
pnpm --filter web analyze
```

### Network Issues
```bash
# Check connectivity
curl -I https://registry.npmjs.org/

# Use different registry
pnpm config set registry https://registry.npmmirror.com/

# Clear DNS cache (macOS)
sudo dscacheutil -flushcache
```

## 📋 Health Check Commands

### System Health
```bash
# Check Node.js and pnpm versions
node --version && pnpm --version

# Verify workspace structure
pnpm list --depth=0

# Check for outdated packages
pnpm outdated

# Audit for vulnerabilities
pnpm audit
```

### Project Health
```bash
# Verify all packages build
pnpm build --dry-run

# Check TypeScript across all packages
pnpm type-check

# Run all tests
pnpm test

# Lint all code
pnpm lint
```

## 🆘 Getting Help

### Community Resources
- [Turborepo Discord](https://discord.gg/turborepo)
- [Next.js Discussions](https://github.com/vercel/next.js/discussions)
- [TanStack Query Discord](https://discord.com/invite/WrRKjPJ)
- [Shadcn/ui GitHub](https://github.com/shadcn-ui/ui)

### Documentation Links
- [Turborepo Docs](https://turbo.build/repo/docs)
- [Next.js Docs](https://nextjs.org/docs)
- [pnpm Docs](https://pnpm.io/motivation)
- [TanStack Query Docs](https://tanstack.com/query/latest)

### Creating Bug Reports
When reporting issues, include:
- Node.js and pnpm versions
- Operating system
- Complete error messages
- Steps to reproduce
- Minimal reproduction repository

---

**🎯 Remember**: Most issues can be resolved by clearing caches, updating dependencies, or checking configuration files. When in doubt, start fresh with a clean install.
