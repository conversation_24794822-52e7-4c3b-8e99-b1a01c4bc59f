# Adding Features

Step-by-step guide for developing new features in the enterprise monorepo, from planning to deployment.

## 🎯 Feature Development Workflow

### 1. Planning Phase
- Define feature requirements
- Design API contracts
- Plan component structure
- Identify shared utilities needed

### 2. Implementation Phase
- Create feature branch
- Implement backend services (if needed)
- Create UI components
- Add state management
- Write tests

### 3. Integration Phase
- Integrate with existing features
- Update documentation
- Performance testing
- Code review

## 🚀 Example: Building a User Management Feature

Let's walk through creating a complete user management feature with CRUD operations.

### Step 1: Create Feature Branch
```bash
git checkout -b feature/user-management
```

### Step 2: Define Types
```typescript
// app/types/user.ts
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'admin' | 'user' | 'viewer'
  status: 'active' | 'inactive' | 'pending'
  createdAt: string
  updatedAt: string
}

export interface CreateUserData {
  name: string
  email: string
  role: User['role']
}

export interface UpdateUserData {
  name?: string
  email?: string
  role?: User['role']
  status?: User['status']
}

export interface UsersResponse {
  users: User[]
  total: number
  page: number
  limit: number
}
```

### Step 3: Create API Service
```typescript
// app/services/user-service.ts
import { api } from './api'
import { User, CreateUserData, UpdateUserData, UsersResponse } from '@/types/user'

export interface GetUsersParams {
  page?: number
  limit?: number
  search?: string
  role?: User['role']
  status?: User['status']
}

export const UserService = {
  getUsers: (params: GetUsersParams = {}) => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString())
      }
    })
    return api.get<UsersResponse>(`/users?${searchParams}`)
  },

  getUser: (id: string) => api.get<User>(`/users/${id}`),

  createUser: (data: CreateUserData) => api.post<User>('/users', data),

  updateUser: (id: string, data: UpdateUserData) =>
    api.patch<User>(`/users/${id}`, data),

  deleteUser: (id: string) => api.delete<void>(`/users/${id}`),

  bulkUpdateUsers: (ids: string[], data: UpdateUserData) =>
    api.patch<User[]>('/users/bulk', { ids, data }),
}
```

### Step 4: Create Query Hooks
```typescript
// app/hooks/queries/use-users-query.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { UserService, GetUsersParams } from '@/services/user-service'
import { CreateUserData, UpdateUserData } from '@/types/user'
import { toast } from '@/components/ui/use-toast'

export const useUsersQuery = (params: GetUsersParams = {}) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => UserService.getUsers(params),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export const useUserQuery = (id: string) => {
  return useQuery({
    queryKey: ['users', id],
    queryFn: () => UserService.getUser(id),
    enabled: !!id,
  })
}

export const useCreateUserMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateUserData) => UserService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast({
        title: 'Success',
        description: 'User created successfully',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create user',
        variant: 'destructive',
      })
    },
  })
}

export const useUpdateUserMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserData }) =>
      UserService.updateUser(id, data),
    onSuccess: (updatedUser) => {
      queryClient.setQueryData(['users', updatedUser.id], updatedUser)
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast({
        title: 'Success',
        description: 'User updated successfully',
      })
    },
  })
}

export const useDeleteUserMutation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => UserService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast({
        title: 'Success',
        description: 'User deleted successfully',
      })
    },
  })
}
```

### Step 5: Create UI Components

#### User List Component
```typescript
// app/components/features/users/user-list.tsx
"use client"

import { useState } from 'react'
import { useUsersQuery } from '@/hooks/queries/use-users-query'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { UserCard } from './user-card'
import { UserForm } from './user-form'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Search } from 'lucide-react'

export function UserList() {
  const [search, setSearch] = useState('')
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  
  const { data, isLoading, error } = useUsersQuery({ search })

  if (isLoading) return <UserListSkeleton />
  if (error) return <div>Error loading users</div>

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Users</h1>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent>
            <UserForm onSuccess={() => setIsCreateOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search users..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {data?.users.map((user) => (
          <UserCard key={user.id} user={user} />
        ))}
      </div>

      {data?.users.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No users found</p>
        </div>
      )}
    </div>
  )
}
```

#### User Card Component
```typescript
// app/components/features/users/user-card.tsx
"use client"

import { useState } from 'react'
import { User } from '@/types/user'
import { useUpdateUserMutation, useDeleteUserMutation } from '@/hooks/queries/use-users-query'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Edit, Trash2 } from 'lucide-react'

interface UserCardProps {
  user: User
}

export function UserCard({ user }: UserCardProps) {
  const [isEditOpen, setIsEditOpen] = useState(false)
  const updateUserMutation = useUpdateUserMutation()
  const deleteUserMutation = useDeleteUserMutation()

  const handleStatusToggle = () => {
    const newStatus = user.status === 'active' ? 'inactive' : 'active'
    updateUserMutation.mutate({ id: user.id, data: { status: newStatus } })
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this user?')) {
      deleteUserMutation.mutate(user.id)
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium">{user.name}</p>
          <p className="text-xs text-gray-500">{user.email}</p>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsEditOpen(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center">
          <div className="space-x-2">
            <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
              {user.role}
            </Badge>
            <Badge 
              variant={user.status === 'active' ? 'default' : 'secondary'}
              className="cursor-pointer"
              onClick={handleStatusToggle}
            >
              {user.status}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
```

#### User Form Component
```typescript
// app/components/features/users/user-form.tsx
"use client"

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useCreateUserMutation, useUpdateUserMutation } from '@/hooks/queries/use-users-query'
import { User } from '@/types/user'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

const userSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  role: z.enum(['admin', 'user', 'viewer']),
})

type UserFormData = z.infer<typeof userSchema>

interface UserFormProps {
  user?: User
  onSuccess?: () => void
}

export function UserForm({ user, onSuccess }: UserFormProps) {
  const createUserMutation = useCreateUserMutation()
  const updateUserMutation = useUpdateUserMutation()
  
  const form = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      role: user?.role || 'user',
    },
  })

  const onSubmit = (data: UserFormData) => {
    if (user) {
      updateUserMutation.mutate(
        { id: user.id, data },
        { onSuccess }
      )
    } else {
      createUserMutation.mutate(data, { onSuccess })
    }
  }

  const isLoading = createUserMutation.isPending || updateUserMutation.isPending

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="Enter email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Role</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="flex justify-end space-x-2">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Saving...' : user ? 'Update' : 'Create'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
```

### Step 6: Create Page Route
```typescript
// app/(dashboard)/users/page.tsx
import { UserList } from '@/components/features/users/user-list'

export default function UsersPage() {
  return (
    <div className="container mx-auto py-6">
      <UserList />
    </div>
  )
}
```

### Step 7: Add Navigation
```typescript
// app/components/layout/sidebar.tsx
import Link from 'next/link'
import { Users, Home, Settings } from 'lucide-react'

export function Sidebar() {
  return (
    <nav className="space-y-2">
      <Link href="/dashboard" className="nav-link">
        <Home className="mr-2 h-4 w-4" />
        Dashboard
      </Link>
      <Link href="/users" className="nav-link">
        <Users className="mr-2 h-4 w-4" />
        Users
      </Link>
      <Link href="/settings" className="nav-link">
        <Settings className="mr-2 h-4 w-4" />
        Settings
      </Link>
    </nav>
  )
}
```

### Step 8: Write Tests
```typescript
// app/components/features/users/__tests__/user-card.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { UserCard } from '../user-card'
import { User } from '@/types/user'

const mockUser: User = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  status: 'active',
  createdAt: '2023-01-01',
  updatedAt: '2023-01-01',
}

const queryClient = new QueryClient({
  defaultOptions: { queries: { retry: false } },
})

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
)

describe('UserCard', () => {
  it('renders user information', () => {
    render(<UserCard user={mockUser} />, { wrapper: Wrapper })
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('user')).toBeInTheDocument()
    expect(screen.getByText('active')).toBeInTheDocument()
  })

  it('opens dropdown menu on click', () => {
    render(<UserCard user={mockUser} />, { wrapper: Wrapper })
    
    const menuButton = screen.getByRole('button')
    fireEvent.click(menuButton)
    
    expect(screen.getByText('Edit')).toBeInTheDocument()
    expect(screen.getByText('Delete')).toBeInTheDocument()
  })
})
```

### Step 9: Update Documentation
```markdown
# User Management Feature

## Overview
Complete CRUD operations for user management with role-based access control.

## Components
- `UserList`: Main list view with search and filtering
- `UserCard`: Individual user display with actions
- `UserForm`: Create/edit user form with validation

## API Endpoints
- `GET /users` - List users with pagination and filtering
- `GET /users/:id` - Get single user
- `POST /users` - Create new user
- `PATCH /users/:id` - Update user
- `DELETE /users/:id` - Delete user

## Permissions
- Admin: Full CRUD access
- User: Read access to own profile
- Viewer: Read-only access
```

## 📋 Feature Development Checklist

### Planning
- [ ] Define feature requirements
- [ ] Design API contracts
- [ ] Plan component hierarchy
- [ ] Identify reusable components

### Implementation
- [ ] Create TypeScript types
- [ ] Implement API service layer
- [ ] Create query/mutation hooks
- [ ] Build UI components
- [ ] Add form validation
- [ ] Implement error handling

### Testing
- [ ] Write unit tests for components
- [ ] Write integration tests for hooks
- [ ] Test error scenarios
- [ ] Test loading states

### Documentation
- [ ] Update API documentation
- [ ] Document component props
- [ ] Add usage examples
- [ ] Update navigation

### Review
- [ ] Code review
- [ ] Performance testing
- [ ] Accessibility testing
- [ ] Mobile responsiveness

---

**Next:** [Testing Strategy](./12-testing-strategy.md) for comprehensive testing approaches.
