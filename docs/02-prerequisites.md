# Prerequisites

Before implementing the enterprise monorepo architecture, ensure you have the required tools, knowledge, and environment setup.

## 🛠️ Required Tools

### Node.js (18+ LTS)
**Why:** Runtime for the entire JavaScript ecosystem.

```bash
# Check current version
node --version

# Install via Node Version Manager (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install --lts
nvm use --lts
```

**Alternative installations:**
- [Official Node.js installer](https://nodejs.org/)
- [Volta](https://volta.sh/) (JavaScript tool manager)
- [fnm](https://github.com/Schniz/fnm) (Fast Node Manager)

### pnpm (9+)
**Why:** Fast, disk space efficient package manager with excellent monorepo support.


```bash
# Verify installation
pnpm --version

# Install pnpm
npm install -g pnpm

# Or via corepack (Node.js 16.13+)
corepack enable
corepack prepare pnpm@latest --activate

# Verify installation
pnpm --version
```

**Benefits over npm/yarn:**
- 3x faster installations
- Saves disk space with content-addressable storage
- Better monorepo workspace support
- Strict dependency resolution

### Git
**Why:** Version control and collaboration.

```bash
# Check version
git --version

```

### Code Editor (VS Code Recommended)

**Essential Extensions:**
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

## 📚 Required Knowledge

### JavaScript/TypeScript (Intermediate)
- ES6+ features (destructuring, async/await, modules)
- TypeScript basics (types, interfaces, generics)
- Understanding of promises and async programming

**Resources:**
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Modern JavaScript Tutorial](https://javascript.info/)

### React (Intermediate)
- Functional components and hooks
- Context API basics
- Understanding of component lifecycle
- Event handling and state management

**Key concepts for this architecture:**
- React Server Components (RSC)
- Client vs Server components
- Suspense boundaries

**Resources:**
- [React Documentation](https://react.dev/)
- [React Server Components](https://react.dev/blog/2023/03/22/react-labs-what-we-have-been-working-on-march-2023#react-server-components)

### Next.js (Basic to Intermediate)
- App Router (not Pages Router)
- File-based routing
- Server and Client components
- API routes (optional for this architecture)

**Resources:**
- [Next.js Documentation](https://nextjs.org/docs)
- [App Router Guide](https://nextjs.org/docs/app)

### CSS/Tailwind CSS (Basic)
- CSS fundamentals (flexbox, grid, positioning)
- Tailwind utility classes
- Responsive design principles

**Resources:**
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/)

## 🌐 Environment Setup

### Terminal/Shell
**macOS/Linux:** Use the default terminal or upgrade to:
- [iTerm2](https://iterm2.com/) (macOS)
- [Hyper](https://hyper.is/) (Cross-platform)
- [Warp](https://www.warp.dev/) (Modern terminal)

**Windows:** 
- [Windows Terminal](https://aka.ms/terminal)
- [WSL2](https://docs.microsoft.com/en-us/windows/wsl/install) (recommended)

### Shell Enhancement (Optional)
```bash
# Oh My Zsh (macOS/Linux)
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

# Useful aliases for this project
echo 'alias dev="pnpm dev"' >> ~/.zshrc
echo 'alias build="pnpm build"' >> ~/.zshrc
echo 'alias ui="pnpm --filter web dlx shadcn-ui@latest add"' >> ~/.zshrc
```

## 🔧 Development Environment

### Browser Developer Tools
**Chrome DevTools Extensions:**
- React Developer Tools
- TanStack Query DevTools (built into our setup)
- Redux DevTools (if using Redux)

### Database (Optional)
For full-stack development:
- **PostgreSQL** (recommended for production)
- **SQLite** (good for development)
- **Supabase** (PostgreSQL with real-time features)

### API Testing
- **Postman** or **Insomnia** for API testing
- **curl** for command-line testing

## 📋 Knowledge Validation Checklist

Before proceeding, ensure you can:

**JavaScript/TypeScript:**
- [ ] Write and understand TypeScript interfaces
- [ ] Use async/await for asynchronous operations
- [ ] Destructure objects and arrays
- [ ] Understand module imports/exports

**React:**
- [ ] Create functional components with hooks
- [ ] Manage state with useState and useEffect
- [ ] Pass props between components
- [ ] Handle events (onClick, onChange, etc.)

**Next.js:**
- [ ] Understand the difference between Server and Client components
- [ ] Create pages using the App Router
- [ ] Use Next.js Link component for navigation

**CSS/Tailwind:**
- [ ] Apply basic Tailwind utility classes
- [ ] Create responsive layouts
- [ ] Understand flexbox and grid basics

**Command Line:**
- [ ] Navigate directories (cd, ls, pwd)
- [ ] Run npm/pnpm commands
- [ ] Use git for version control

## 🚨 Common Setup Issues

### Node.js Version Conflicts
```bash
# Use nvm to manage versions
nvm list
nvm use 18.17.0  # or latest LTS
```

### pnpm Permission Issues
```bash
# Fix npm permissions (macOS/Linux)
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.profile
source ~/.profile
```

### VS Code TypeScript Issues
```bash
# Ensure VS Code uses workspace TypeScript
# Cmd/Ctrl + Shift + P → "TypeScript: Select TypeScript Version" → "Use Workspace Version"
```

## 🎯 Ready to Start?

Once you have:
- ✅ Node.js 18+ installed
- ✅ pnpm 9+ installed  
- ✅ Git configured
- ✅ VS Code with recommended extensions
- ✅ Basic knowledge of the required technologies

You're ready to proceed to the [Quick Start Guide](./01-quick-start.md)!

## 📞 Getting Help

**Tool Installation Issues:**
- Node.js: [Official troubleshooting](https://nodejs.org/en/docs/guides/debugging-getting-started/)
- pnpm: [Installation guide](https://pnpm.io/installation)

**Learning Resources:**
- [Frontend Masters](https://frontendmasters.com/)
- [Egghead.io](https://egghead.io/)
- [React Documentation](https://react.dev/)
- [Next.js Learn](https://nextjs.org/learn)

---

**Next:** [Quick Start Guide](./01-quick-start.md) to begin implementation.
