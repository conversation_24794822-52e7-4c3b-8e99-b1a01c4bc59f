# Quick Start Guide

Get your enterprise monorepo up and running in 15 minutes.

## 🎯 What You'll Build

By the end of this guide, you'll have:
- A fully functional monorepo with Turborepo
- A Next.js app with Shadcn/ui components
- State management with TanStack Query and Zustand
- A working dashboard with authentication routes

## ⚡ Prerequisites

Ensure you have these installed:
- **Node.js 18+** (LTS recommended)
- **pnpm 9+** (package manager)
- **Git** (version control)

```bash
# Check versions
node --version  # Should be 18+
pnpm --version  # Should be 9+
git --version
```

## 🚀 Step 1: Initialize the Monorepo

```bash
# Create project directory
mkdir enterprise-monorepo
cd enterprise-monorepo

# Initialize git
git init

# Create basic structure
mkdir -p apps/web apps/docs packages/{config-eslint,config-tailwind,config-typescript,lib-utils}
```

## 📦 Step 2: Setup Root Configuration

Create the root `package.json`:

```bash
pnpm init
```

Replace the generated content with:

```json
{
  "name": "enterprise-monorepo",
  "private": true,
  "scripts": {
    "build": "turbo build",
    "dev": "turbo dev",
    "lint": "turbo lint",
    "format": "prettier --write \"**/*.{ts,tsx,md}\"",
    "clean": "git clean -xdf node_modules"
  },
  "devDependencies": {
    "prettier": "^3.2.5",
    "turbo": "^2.0.4",
    "typescript": "^5.4.5"
  },
  "packageManager": "pnpm@9.1.4"
}
```

Create `pnpm-workspace.yaml`:

```yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

Create `turbo.json`:

```json
{
  "$schema": "https://turbo.build/schema.json",
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "!.next/cache/**", "dist/**"]
    },
    "lint": {
      "dependsOn": ["^build"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    }
  }
}
```

## 🏗️ Step 3: Install Root Dependencies

```bash
pnpm install
```

## 📱 Step 4: Create the Next.js App

```bash
cd apps/web
pnpm create next-app@latest . --typescript --tailwind --eslint --app --src-dir=false --import-alias="@/*"
```

When prompted:
- ✅ TypeScript
- ✅ ESLint  
- ✅ Tailwind CSS
- ✅ App Router
- ❌ src/ directory
- ✅ Import alias (@/*)

## 🎨 Step 5: Setup Shadcn/ui

```bash
# Still in apps/web
pnpm dlx shadcn-ui@latest init
```

Configuration options:
- Style: **Default**
- Base color: **Slate**
- CSS variables: **Yes**

Add your first components:

```bash
pnpm dlx shadcn-ui@latest add button card
```

## 🔧 Step 6: Install Core Dependencies

```bash
# In apps/web
pnpm add @tanstack/react-query zustand class-variance-authority clsx tailwind-merge lucide-react
pnpm add -D @tanstack/react-query-devtools
```

## 🏃 Step 7: Test the Setup

```bash
# From the root directory
cd ../..
pnpm dev
```

You should see:
- Turborepo starting the dev server
- Next.js running on http://localhost:3000
- Hot reload working

## 🎉 Step 8: Verify Everything Works

1. **Open your browser** to http://localhost:3000
2. **See the Next.js welcome page**
3. **Check hot reload** by editing `apps/web/app/page.tsx`
4. **Verify Tailwind** classes are working

## 🔍 What's Next?

Your basic setup is complete! Now you can:

1. **[Setup Shared Packages](./16-package-management.md)** - Create reusable configurations
2. **[Add State Management](./09-state-management.md)** - Implement TanStack Query and Zustand
3. **[Create Your First Feature](./11-adding-features.md)** - Build a dashboard page
4. **[Setup Authentication](./07-nextjs-app-router.md)** - Add login/signup flows

## 🐛 Troubleshooting

**pnpm not found?**
```bash
npm install -g pnpm
```

**Port 3000 already in use?**
```bash
# Kill the process using port 3000
lsof -ti:3000 | xargs kill -9
```

**TypeScript errors?**
```bash
# Clear Next.js cache
rm -rf apps/web/.next
pnpm dev
```

## 📚 Quick Reference

```bash
# Development
pnpm dev              # Start all apps in dev mode
pnpm build            # Build all apps
pnpm lint             # Lint all packages

# Adding dependencies
pnpm add <package>    # Add to root
pnpm --filter web add <package>  # Add to specific app

# Shadcn/ui
pnpm --filter web dlx shadcn-ui@latest add <component>
```

---

**🎯 Success!** You now have a production-ready monorepo foundation. Continue with the [Project Setup](./03-project-setup.md) guide for detailed configuration.
