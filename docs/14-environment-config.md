# Environment Configuration

Comprehensive guide for managing environment variables, configurations, and secrets across different deployment environments.

## 🎯 Environment Strategy

### Environment Hierarchy
```mermaid
graph TD
    A[Local Development] --> B[Staging]
    B --> C[Production]
    
    subgraph "Configuration Sources"
        D[.env files]
        E[CI/CD Variables]
        F[Platform Secrets]
        G[External Services]
    end
    
    A --> D
    B --> E
    C --> F
    C --> G
```

### Environment Types
- **Development**: Local development with hot reload
- **Test**: Automated testing environment
- **Staging**: Production-like environment for testing
- **Production**: Live application environment

## 🔧 Environment Variables Setup

### Environment Schema Validation
```typescript
// apps/web/lib/env.ts
import { z } from 'zod'

const envSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'test', 'staging', 'production']).default('development'),
  
  // Application URLs
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXT_PUBLIC_API_URL: z.string().url(),
  
  // Database
  DATABASE_URL: z.string().url(),
  DATABASE_POOL_SIZE: z.coerce.number().default(10),
  
  // Authentication
  NEXTAUTH_SECRET: z.string().min(32),
  NEXTAUTH_URL: z.string().url(),
  
  // OAuth Providers
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  GITHUB_CLIENT_ID: z.string().optional(),
  GITHUB_CLIENT_SECRET: z.string().optional(),
  
  // External Services
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  SENDGRID_API_KEY: z.string().optional(),
  
  // Monitoring
  NEXT_PUBLIC_SENTRY_DSN: z.string().optional(),
  VERCEL_ANALYTICS_ID: z.string().optional(),
  
  // Feature Flags
  NEXT_PUBLIC_ENABLE_ANALYTICS: z.coerce.boolean().default(false),
  NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE: z.coerce.boolean().default(false),
  
  // Redis (optional)
  REDIS_URL: z.string().optional(),
  
  // File Storage
  AWS_ACCESS_KEY_ID: z.string().optional(),
  AWS_SECRET_ACCESS_KEY: z.string().optional(),
  AWS_REGION: z.string().default('us-east-1'),
  AWS_S3_BUCKET: z.string().optional(),
})

export type Env = z.infer<typeof envSchema>

function validateEnv(): Env {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => err.path.join('.')).join(', ')
      throw new Error(`Missing or invalid environment variables: ${missingVars}`)
    }
    throw error
  }
}

export const env = validateEnv()
```

### Environment Files Structure

#### Development (.env.local)
```bash
# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/myapp_dev
DATABASE_POOL_SIZE=5

# Authentication
NEXTAUTH_SECRET=development-secret-key-min-32-chars
NEXTAUTH_URL=http://localhost:3000

# OAuth (Development)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# External Services (Test Keys)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
SENDGRID_API_KEY=SG....

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE=false

# Redis (Optional)
REDIS_URL=redis://localhost:6379

# File Storage (Local/Development)
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_REGION=us-east-1
AWS_S3_BUCKET=myapp-dev
```

#### Test (.env.test)
```bash
# Test Environment
NODE_ENV=test
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001

# Test Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/myapp_test
DATABASE_POOL_SIZE=2

# Test Authentication
NEXTAUTH_SECRET=test-secret-key-for-testing-only
NEXTAUTH_URL=http://localhost:3000

# Disable external services in tests
NEXT_PUBLIC_ENABLE_ANALYTICS=false
```

#### Staging (.env.staging)
```bash
# Staging Environment
NODE_ENV=staging
NEXT_PUBLIC_APP_URL=https://staging.myapp.com
NEXT_PUBLIC_API_URL=https://api-staging.myapp.com

# Staging Database
DATABASE_URL=**************************************/myapp_staging
DATABASE_POOL_SIZE=10

# Authentication
NEXTAUTH_SECRET=staging-secret-key-different-from-prod
NEXTAUTH_URL=https://staging.myapp.com

# OAuth (Staging Apps)
GOOGLE_CLIENT_ID=staging-google-client-id
GOOGLE_CLIENT_SECRET=staging-google-client-secret

# External Services (Test Mode)
STRIPE_SECRET_KEY=sk_test_...
SENDGRID_API_KEY=SG....

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://...@sentry.io/staging

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE=false
```

#### Production (.env.production)
```bash
# Production Environment
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://myapp.com
NEXT_PUBLIC_API_URL=https://api.myapp.com

# Production Database
DATABASE_URL=***********************************/myapp_prod
DATABASE_POOL_SIZE=20

# Authentication
NEXTAUTH_SECRET=super-secure-production-secret-key
NEXTAUTH_URL=https://myapp.com

# OAuth (Production Apps)
GOOGLE_CLIENT_ID=prod-google-client-id
GOOGLE_CLIENT_SECRET=prod-google-client-secret

# External Services (Live Keys)
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
SENDGRID_API_KEY=SG....

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://...@sentry.io/production
VERCEL_ANALYTICS_ID=your-analytics-id

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE=false

# Redis
REDIS_URL=redis://prod-redis:6379

# File Storage
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=...
AWS_REGION=us-east-1
AWS_S3_BUCKET=myapp-prod
```

## 🔐 Secrets Management

### Vercel Environment Variables
```bash
# Set environment variables for Vercel
vercel env add NEXTAUTH_SECRET production
vercel env add DATABASE_URL production
vercel env add STRIPE_SECRET_KEY production

# Pull environment variables locally
vercel env pull .env.local
```

### GitHub Actions Secrets
```yaml
# .github/workflows/deploy.yml
env:
  DATABASE_URL: ${{ secrets.DATABASE_URL }}
  NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
  STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
```

### AWS Secrets Manager Integration
```typescript
// apps/web/lib/secrets.ts
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager'

const client = new SecretsManagerClient({ region: 'us-east-1' })

export async function getSecret(secretName: string): Promise<string> {
  try {
    const command = new GetSecretValueCommand({ SecretId: secretName })
    const response = await client.send(command)
    return response.SecretString || ''
  } catch (error) {
    console.error(`Failed to retrieve secret ${secretName}:`, error)
    throw error
  }
}

// Usage in API routes
export async function getStripeKey(): Promise<string> {
  if (process.env.NODE_ENV === 'development') {
    return process.env.STRIPE_SECRET_KEY || ''
  }
  return getSecret('prod/stripe/secret-key')
}
```

## 🏗️ Configuration Management

### Feature Flags System
```typescript
// apps/web/lib/feature-flags.ts
import { env } from './env'

export interface FeatureFlags {
  enableAnalytics: boolean
  enableMaintenanceMode: boolean
  enableNewDashboard: boolean
  enableBetaFeatures: boolean
}

export function getFeatureFlags(): FeatureFlags {
  return {
    enableAnalytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    enableMaintenanceMode: env.NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE,
    enableNewDashboard: env.NODE_ENV !== 'production', // Beta in non-prod
    enableBetaFeatures: env.NODE_ENV === 'development',
  }
}

// Hook for using feature flags
export function useFeatureFlags() {
  return getFeatureFlags()
}
```

### Configuration Provider
```typescript
// apps/web/lib/config-provider.tsx
'use client'

import { createContext, useContext } from 'react'
import { env } from './env'
import { getFeatureFlags, FeatureFlags } from './feature-flags'

interface AppConfig {
  env: typeof env
  featureFlags: FeatureFlags
  apiUrl: string
  appUrl: string
}

const ConfigContext = createContext<AppConfig | null>(null)

export function ConfigProvider({ children }: { children: React.ReactNode }) {
  const config: AppConfig = {
    env,
    featureFlags: getFeatureFlags(),
    apiUrl: env.NEXT_PUBLIC_API_URL,
    appUrl: env.NEXT_PUBLIC_APP_URL,
  }

  return (
    <ConfigContext.Provider value={config}>
      {children}
    </ConfigContext.Provider>
  )
}

export function useConfig() {
  const config = useContext(ConfigContext)
  if (!config) {
    throw new Error('useConfig must be used within ConfigProvider')
  }
  return config
}
```

## 🔄 Environment-Specific Configurations

### Database Configuration
```typescript
// apps/web/lib/database.ts
import { env } from './env'

export const databaseConfig = {
  url: env.DATABASE_URL,
  pool: {
    min: 0,
    max: env.DATABASE_POOL_SIZE,
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
  },
  migrations: {
    directory: './migrations',
    tableName: 'knex_migrations',
  },
  seeds: {
    directory: './seeds',
  },
}

// Environment-specific overrides
if (env.NODE_ENV === 'test') {
  databaseConfig.pool.max = 1
  databaseConfig.migrations.directory = './test-migrations'
}

if (env.NODE_ENV === 'production') {
  databaseConfig.pool.min = 2
  databaseConfig.pool.max = 20
}
```

### API Configuration
```typescript
// apps/web/lib/api-config.ts
import { env } from './env'

export const apiConfig = {
  baseUrl: env.NEXT_PUBLIC_API_URL,
  timeout: env.NODE_ENV === 'production' ? 30000 : 60000,
  retries: env.NODE_ENV === 'production' ? 3 : 1,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Version': '1.0',
  },
}

// Rate limiting configuration
export const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: env.NODE_ENV === 'production' ? 100 : 1000, // requests per window
  message: 'Too many requests from this IP',
}
```

## 🧪 Environment Testing

### Environment Validation Tests
```typescript
// apps/web/__tests__/env.test.ts
import { env } from '../lib/env'

describe('Environment Configuration', () => {
  it('should have all required environment variables', () => {
    expect(env.NODE_ENV).toBeDefined()
    expect(env.NEXT_PUBLIC_APP_URL).toBeDefined()
    expect(env.NEXT_PUBLIC_API_URL).toBeDefined()
    expect(env.DATABASE_URL).toBeDefined()
    expect(env.NEXTAUTH_SECRET).toBeDefined()
  })

  it('should have valid URLs', () => {
    expect(() => new URL(env.NEXT_PUBLIC_APP_URL)).not.toThrow()
    expect(() => new URL(env.NEXT_PUBLIC_API_URL)).not.toThrow()
    expect(() => new URL(env.NEXTAUTH_URL)).not.toThrow()
  })

  it('should have secure secrets in production', () => {
    if (env.NODE_ENV === 'production') {
      expect(env.NEXTAUTH_SECRET.length).toBeGreaterThanOrEqual(32)
      expect(env.NEXTAUTH_SECRET).not.toContain('development')
      expect(env.NEXTAUTH_SECRET).not.toContain('test')
    }
  })
})
```

## 📋 Environment Checklist

### Development Setup
- [ ] `.env.local` file created
- [ ] All required variables defined
- [ ] Database connection working
- [ ] External services configured (test mode)
- [ ] Feature flags set appropriately

### Staging Deployment
- [ ] Staging environment variables set
- [ ] Database migrations applied
- [ ] External services configured (test mode)
- [ ] Monitoring enabled
- [ ] Feature flags configured

### Production Deployment
- [ ] Production secrets secured
- [ ] Database connection pooling configured
- [ ] External services configured (live mode)
- [ ] Monitoring and analytics enabled
- [ ] Feature flags set for production
- [ ] Security headers configured
- [ ] Rate limiting enabled

### Security Checklist
- [ ] No secrets in source code
- [ ] Environment variables validated
- [ ] Secrets rotation plan in place
- [ ] Access controls configured
- [ ] Audit logging enabled

---

**Next:** [Performance Optimization](./15-performance.md) for application performance tuning.
