# Testing Strategy

Comprehensive testing approach for the enterprise monorepo, covering unit tests, integration tests, and end-to-end testing.

## 🎯 Testing Philosophy

### Testing Pyramid
```mermaid
graph TD
    A[E2E Tests - Few] --> B[Integration Tests - Some]
    B --> C[Unit Tests - Many]
    
    style A fill:#ff6b6b
    style B fill:#4ecdc4
    style C fill:#45b7d1
```

### Test Types and Coverage
- **Unit Tests (70%)**: Individual components, hooks, and utilities
- **Integration Tests (20%)**: Component interactions and API integration
- **E2E Tests (10%)**: Critical user journeys and workflows

## 🧪 Testing Stack

### Core Testing Tools
```json
{
  "devDependencies": {
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.1.4",
    "@testing-library/user-event": "^14.5.1",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^29.7.0",
    "@playwright/test": "^1.40.0",
    "msw": "^2.0.0"
  }
}
```

### Testing Framework Setup

#### Jest Configuration
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  collectCoverageFrom: [
    'app/**/*.{js,jsx,ts,tsx}',
    '!app/**/*.d.ts',
    '!app/**/layout.tsx',
    '!app/**/loading.tsx',
    '!app/**/not-found.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}

module.exports = createJestConfig(customJestConfig)
```

#### Jest Setup File
```javascript
// jest.setup.js
import '@testing-library/jest-dom'
import { server } from './app/__mocks__/server'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Setup MSW
beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())
```

## 🔧 Unit Testing

### Component Testing
```typescript
// app/components/ui/__tests__/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies variant styles correctly', () => {
    render(<Button variant="destructive">Delete</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-destructive')
  })

  it('renders as child component when asChild is true', () => {
    render(
      <Button asChild>
        <a href="/test">Link Button</a>
      </Button>
    )
    
    const link = screen.getByRole('link')
    expect(link).toHaveAttribute('href', '/test')
    expect(link).toHaveClass('inline-flex') // Button classes applied
  })
})
```

### Hook Testing
```typescript
// app/hooks/__tests__/use-users-query.test.tsx
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUsersQuery } from '../queries/use-users-query'
import { UserService } from '@/services/user-service'

// Mock the service
jest.mock('@/services/user-service')
const mockUserService = UserService as jest.Mocked<typeof UserService>

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useUsersQuery', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('fetches users successfully', async () => {
    const mockUsers = [
      { id: '1', name: 'John Doe', email: '<EMAIL>' },
      { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
    ]
    
    mockUserService.getUsers.mockResolvedValue({
      users: mockUsers,
      total: 2,
      page: 1,
      limit: 10,
    })

    const { result } = renderHook(() => useUsersQuery(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(result.current.data?.users).toEqual(mockUsers)
    expect(mockUserService.getUsers).toHaveBeenCalledWith({})
  })

  it('handles error states', async () => {
    mockUserService.getUsers.mockRejectedValue(new Error('API Error'))

    const { result } = renderHook(() => useUsersQuery(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    expect(result.current.error).toEqual(new Error('API Error'))
  })
})
```

### Utility Testing
```typescript
// app/lib/__tests__/utils.test.ts
import { cn } from '../utils'

describe('cn utility', () => {
  it('merges class names correctly', () => {
    expect(cn('px-2 py-1', 'px-4')).toBe('py-1 px-4')
  })

  it('handles conditional classes', () => {
    expect(cn('base-class', true && 'conditional-class')).toBe('base-class conditional-class')
    expect(cn('base-class', false && 'conditional-class')).toBe('base-class')
  })

  it('handles arrays and objects', () => {
    expect(cn(['class1', 'class2'], { class3: true, class4: false })).toBe('class1 class2 class3')
  })
})
```

## 🔗 Integration Testing

### API Integration Tests
```typescript
// app/services/__tests__/user-service.integration.test.ts
import { UserService } from '../user-service'
import { server } from '../../__mocks__/server'
import { rest } from 'msw'

describe('UserService Integration', () => {
  it('creates user with correct API call', async () => {
    const newUser = { name: 'John Doe', email: '<EMAIL>', role: 'user' as const }
    const createdUser = { id: '1', ...newUser, createdAt: '2023-01-01', updatedAt: '2023-01-01' }

    server.use(
      rest.post('/api/users', async (req, res, ctx) => {
        const body = await req.json()
        expect(body).toEqual(newUser)
        return res(ctx.json(createdUser))
      })
    )

    const result = await UserService.createUser(newUser)
    expect(result).toEqual(createdUser)
  })

  it('handles API errors correctly', async () => {
    server.use(
      rest.get('/api/users', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ message: 'Internal Server Error' }))
      })
    )

    await expect(UserService.getUsers()).rejects.toThrow('Internal Server Error')
  })
})
```

### Component Integration Tests
```typescript
// app/components/features/users/__tests__/user-list.integration.test.tsx
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { UserList } from '../user-list'
import { server } from '../../../__mocks__/server'
import { rest } from 'msw'

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('UserList Integration', () => {
  it('displays users and handles search', async () => {
    const mockUsers = [
      { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'user', status: 'active' },
      { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'admin', status: 'active' },
    ]

    server.use(
      rest.get('/api/users', (req, res, ctx) => {
        const search = req.url.searchParams.get('search')
        const filteredUsers = search 
          ? mockUsers.filter(user => user.name.toLowerCase().includes(search.toLowerCase()))
          : mockUsers
        
        return res(ctx.json({ users: filteredUsers, total: filteredUsers.length }))
      })
    )

    const user = userEvent.setup()
    render(<UserList />, { wrapper: createWrapper() })

    // Wait for users to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    })

    // Test search functionality
    const searchInput = screen.getByPlaceholderText('Search users...')
    await user.type(searchInput, 'john')

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
    })
  })
})
```

## 🎭 Mock Service Worker (MSW)

### MSW Setup
```typescript
// app/__mocks__/handlers.ts
import { rest } from 'msw'

export const handlers = [
  // Users API
  rest.get('/api/users', (req, res, ctx) => {
    return res(
      ctx.json({
        users: [
          {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'user',
            status: 'active',
            createdAt: '2023-01-01',
            updatedAt: '2023-01-01',
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      })
    )
  }),

  rest.post('/api/users', async (req, res, ctx) => {
    const user = await req.json()
    return res(
      ctx.json({
        id: Math.random().toString(),
        ...user,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
    )
  }),

  rest.patch('/api/users/:id', async (req, res, ctx) => {
    const { id } = req.params
    const updates = await req.json()
    return res(
      ctx.json({
        id,
        name: 'Updated User',
        email: '<EMAIL>',
        ...updates,
        updatedAt: new Date().toISOString(),
      })
    )
  }),
]
```

```typescript
// app/__mocks__/server.ts
import { setupServer } from 'msw/node'
import { handlers } from './handlers'

export const server = setupServer(...handlers)
```

## 🎪 End-to-End Testing with Playwright

### Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
})
```

### E2E Test Examples
```typescript
// e2e/user-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('User Management', () => {
  test('should create a new user', async ({ page }) => {
    await page.goto('/users')
    
    // Click add user button
    await page.click('text=Add User')
    
    // Fill form
    await page.fill('input[name="name"]', 'Test User')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.selectOption('select[name="role"]', 'user')
    
    // Submit form
    await page.click('button[type="submit"]')
    
    // Verify user was created
    await expect(page.locator('text=Test User')).toBeVisible()
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test('should search users', async ({ page }) => {
    await page.goto('/users')
    
    // Wait for users to load
    await expect(page.locator('[data-testid="user-card"]')).toHaveCount(2)
    
    // Search for specific user
    await page.fill('input[placeholder="Search users..."]', 'john')
    
    // Verify filtered results
    await expect(page.locator('[data-testid="user-card"]')).toHaveCount(1)
    await expect(page.locator('text=John Doe')).toBeVisible()
  })
})
```

## 📊 Test Scripts and Commands

### Package.json Scripts
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:headed": "playwright test --headed"
  }
}
```

### Turborepo Integration
```json
{
  "pipeline": {
    "test": {
      "dependsOn": ["^build"],
      "outputs": ["coverage/**"]
    },
    "test:e2e": {
      "dependsOn": ["build"],
      "cache": false
    }
  }
}
```

## 🎯 Testing Best Practices

### Component Testing Guidelines
1. **Test behavior, not implementation**
2. **Use semantic queries** (getByRole, getByLabelText)
3. **Test user interactions** with user-event
4. **Mock external dependencies**
5. **Test error states and edge cases**

### Query Testing Patterns
1. **Mock API responses** with MSW
2. **Test loading states**
3. **Test error handling**
4. **Test optimistic updates**
5. **Verify cache invalidation**

### E2E Testing Strategy
1. **Test critical user journeys**
2. **Focus on happy paths**
3. **Test cross-browser compatibility**
4. **Use data-testid for stable selectors**
5. **Keep tests independent**

## 📋 Testing Checklist

### Before Committing
- [ ] All unit tests pass
- [ ] Coverage threshold met (80%)
- [ ] Integration tests pass
- [ ] No console errors in tests

### Before Deploying
- [ ] E2E tests pass
- [ ] Performance tests pass
- [ ] Accessibility tests pass
- [ ] Cross-browser testing complete

---

**Next:** [Deployment](./13-deployment.md) for production deployment strategies.
