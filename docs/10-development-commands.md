# Development Commands

Essential commands and scripts for daily development workflow in the enterprise monorepo.

## 🚀 Quick Reference

### Daily Commands
```bash
# Start development servers
pnpm dev

# Build all packages
pnpm build

# Run tests
pnpm test

# Lint code
pnpm lint

# Format code
pnpm format
```

### Package-Specific Commands
```bash
# Work with specific packages
pnpm --filter web dev
pnpm --filter web build
pnpm --filter web test
```

## 📦 Package Management

### Installing Dependencies

#### Root Dependencies (Development Tools)
```bash
# Add development tools to root
pnpm add -DW prettier eslint typescript

# Add workspace dependencies
pnpm add -W @types/node
```

#### Application Dependencies
```bash
# Add to specific app
pnpm --filter web add react-hook-form
pnpm --filter web add -D @types/react

# Add to multiple apps
pnpm --filter "{web,docs}" add lodash
```

#### Shared Package Dependencies
```bash
# Add to shared package
pnpm --filter config-typescript add typescript
pnpm --filter lib-utils add date-fns
```

### Removing Dependencies
```bash
# Remove from specific package
pnpm --filter web remove unused-package

# Remove from root
pnpm remove -W unused-dev-tool
```

### Updating Dependencies
```bash
# Update all dependencies
pnpm update

# Update specific package
pnpm --filter web update react

# Update to latest versions
pnpm --filter web update --latest
```

## 🏗️ Build Commands

### Development Builds
```bash
# Start all apps in development
pnpm dev

# Start specific app
pnpm --filter web dev

# Start with specific port
pnpm --filter web dev -- --port 3001

# Start with environment variables
NODE_ENV=development pnpm dev
```

### Production Builds
```bash
# Build all packages
pnpm build

# Build specific package
pnpm --filter web build

# Build with dependencies
pnpm --filter web... build

# Clean build (no cache)
pnpm build --force
```

### Build Analysis
```bash
# Analyze build performance
turbo build --profile

# Generate build summary
turbo build --summarize

# Dry run (see what would execute)
turbo build --dry-run

# Verbose output
turbo build --verbosity=2
```

## 🧪 Testing Commands

### Running Tests
```bash
# Run all tests
pnpm test

# Run tests for specific package
pnpm --filter web test

# Run tests in watch mode
pnpm --filter web test:watch

# Run tests with coverage
pnpm test:coverage
```

### Test Types
```bash
# Unit tests
pnpm test:unit

# Integration tests
pnpm test:integration

# End-to-end tests
pnpm test:e2e

# Visual regression tests
pnpm test:visual
```

## 🔍 Code Quality

### Linting
```bash
# Lint all packages
pnpm lint

# Lint specific package
pnpm --filter web lint

# Lint and fix
pnpm lint:fix

# Lint staged files only
pnpm lint:staged
```

### Formatting
```bash
# Format all files
pnpm format

# Format specific files
pnpm format "apps/web/**/*.{ts,tsx}"

# Check formatting without fixing
pnpm format:check
```

### Type Checking
```bash
# Type check all packages
pnpm type-check

# Type check specific package
pnpm --filter web type-check

# Type check in watch mode
pnpm --filter web type-check:watch
```

## 🎨 UI Development

### Shadcn/ui Commands
```bash
# Add new component
pnpm --filter web dlx shadcn-ui@latest add button

# Add multiple components
pnpm --filter web dlx shadcn-ui@latest add button card input form

# Update components
pnpm --filter web dlx shadcn-ui@latest update

# List available components
pnpm --filter web dlx shadcn-ui@latest list
```

### Storybook (Optional)
```bash
# Start Storybook
pnpm --filter web storybook

# Build Storybook
pnpm --filter web build-storybook

# Test Storybook
pnpm --filter web test-storybook
```

## 🔧 Utility Commands

### Cache Management
```bash
# Clear Turborepo cache
turbo prune

# Clear pnpm cache
pnpm store prune

# Clear Next.js cache
rm -rf apps/web/.next

# Clear all caches
pnpm clean
```

### Dependency Analysis
```bash
# List dependencies
pnpm list

# List dependencies for specific package
pnpm --filter web list

# Check for outdated packages
pnpm outdated

# Audit dependencies
pnpm audit

# Fix audit issues
pnpm audit --fix
```

### Workspace Information
```bash
# List all packages
pnpm list --depth=0

# Show workspace info
pnpm list --json

# Check package sizes
pnpm --filter web exec -- du -sh node_modules
```

## 🚀 Deployment Commands

### Production Preparation
```bash
# Full production build
NODE_ENV=production pnpm build

# Test production build locally
pnpm --filter web start

# Bundle analysis
pnpm --filter web analyze
```

### Environment-Specific Builds
```bash
# Development build
NODE_ENV=development pnpm build

# Staging build
NODE_ENV=staging pnpm build

# Production build
NODE_ENV=production pnpm build
```

## 🔄 Git Workflow

### Pre-commit Hooks
```bash
# Run pre-commit checks
pnpm pre-commit

# Skip pre-commit hooks (not recommended)
git commit --no-verify
```

### Branch Management
```bash
# Create feature branch
git checkout -b feature/new-feature

# Update from main
git checkout main
git pull origin main
git checkout feature/new-feature
git rebase main
```

## 📊 Monitoring and Debugging

### Performance Monitoring
```bash
# Profile build performance
turbo build --profile=profile.json

# Analyze profile
turbo analyze profile.json

# Monitor bundle size
pnpm --filter web exec -- npx bundlesize
```

### Debugging
```bash
# Debug Next.js
pnpm --filter web dev -- --inspect

# Debug with specific Node options
NODE_OPTIONS="--inspect" pnpm dev

# Verbose logging
DEBUG=* pnpm dev
```

## 🛠️ Custom Scripts

### Package.json Scripts
```json
{
  "scripts": {
    "dev": "turbo dev",
    "build": "turbo build",
    "test": "turbo test",
    "lint": "turbo lint",
    "lint:fix": "turbo lint -- --fix",
    "format": "prettier --write \"**/*.{ts,tsx,md}\"",
    "format:check": "prettier --check \"**/*.{ts,tsx,md}\"",
    "type-check": "turbo type-check",
    "clean": "turbo clean && rm -rf node_modules",
    "reset": "pnpm clean && pnpm install",
    "pre-commit": "lint-staged",
    "prepare": "husky install"
  }
}
```

### Useful Aliases
```bash
# Add to your shell profile (.bashrc, .zshrc)
alias pdev="pnpm dev"
alias pbuild="pnpm build"
alias ptest="pnpm test"
alias plint="pnpm lint"
alias pformat="pnpm format"

# Package-specific aliases
alias webdev="pnpm --filter web dev"
alias webbuild="pnpm --filter web build"
alias webtest="pnpm --filter web test"
```

## 🔍 Troubleshooting Commands

### Common Issues
```bash
# Port already in use
lsof -ti:3000 | xargs kill -9

# Permission issues
sudo chown -R $(whoami) ~/.pnpm

# Corrupted lock file
rm pnpm-lock.yaml && pnpm install

# Node modules issues
rm -rf node_modules && pnpm install

# TypeScript issues
pnpm --filter web exec -- tsc --noEmit
```

### Health Checks
```bash
# Check Node.js version
node --version

# Check pnpm version
pnpm --version

# Check workspace configuration
pnpm list --depth=0

# Verify builds work
pnpm build --dry-run
```

## 📋 Daily Workflow Checklist

### Starting Development
- [ ] `git pull origin main`
- [ ] `pnpm install` (if dependencies changed)
- [ ] `pnpm dev`
- [ ] Check for any errors in terminal

### Before Committing
- [ ] `pnpm lint`
- [ ] `pnpm type-check`
- [ ] `pnpm test`
- [ ] `pnpm build` (optional, for critical changes)

### Code Review Preparation
- [ ] `pnpm format`
- [ ] `pnpm lint:fix`
- [ ] Ensure all tests pass
- [ ] Update documentation if needed

---

**Next:** [Adding Features](./11-adding-features.md) for step-by-step feature development guide.
